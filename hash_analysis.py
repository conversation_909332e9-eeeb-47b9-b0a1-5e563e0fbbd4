#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
哈希长度异常分析 - 寻找真正的加密模式
"""

import urllib.parse
import base64
import hashlib
import binascii

def analyze_unusual_hash_lengths():
    print("🔍 异常哈希长度分析")
    print("="*60)
    
    samples = [
        ("rT5jnJ1gq2EZLJ1lM2I0pTy1rSZ%3Q", 20, "正常SHA1长度"),
        ("nGA4ZaNjoGW3DRkSoIWaGaERnIA4nD%3Q%3Q", 22, "异常长度!"),
        ("rUAjp21Nq3OZMJ1lM250MTymrTx%3Q", 20, "正常SHA1长度"),
        ("q3AZp21NM3O0Lzy1rTt%3Q", 14, "异常长度!"),
        ("pQWgBKp5GQSgq2qyqTScpauj", 18, "异常长度!"),
    ]
    
    for sample, expected_len, note in samples:
        print(f"\n样本: {sample}")
        print(f"预期长度: {expected_len} bytes - {note}")
        
        try:
            decoded = base64.b64decode(sample.replace("%3Q", "="))
            actual_len = len(decoded)
            print(f"实际长度: {actual_len} bytes")
            print(f"十六进制: {decoded.hex()}")
            
            if actual_len != expected_len:
                print(f"⚠️ 长度不匹配! 预期{expected_len}, 实际{actual_len}")
                
                # 分析可能的原因
                if actual_len == 22:
                    print("💡 可能是: SHA1 + 2字节盐值")
                elif actual_len == 14:
                    print("💡 可能是: 截断的哈希或自定义算法")
                elif actual_len == 18:
                    print("💡 可能是: 截断的SHA1或自定义长度")
            
            # 尝试分析模式
            analyze_hex_patterns(decoded.hex())
            
        except Exception as e:
            print(f"解码失败: {e}")

def analyze_hex_patterns(hex_string):
    """分析十六进制模式"""
    print(f"  模式分析:")
    
    # 检查重复模式
    if len(hex_string) >= 4:
        for i in range(2, len(hex_string)//2 + 1):
            pattern = hex_string[:i]
            if hex_string.startswith(pattern * (len(hex_string)//i)):
                print(f"    重复模式: {pattern}")
    
    # 检查常见前缀/后缀
    common_prefixes = ["00", "ff", "01", "02", "03"]
    common_suffixes = ["00", "ff"]
    
    for prefix in common_prefixes:
        if hex_string.startswith(prefix):
            print(f"    常见前缀: {prefix}")
    
    for suffix in common_suffixes:
        if hex_string.endswith(suffix):
            print(f"    常见后缀: {suffix}")

def test_custom_algorithms():
    print(f"\n🧪 测试自定义算法")
    print("="*60)
    
    test_passwords = ["admin", "123456", "password", "test"]
    
    # 22字节哈希的目标
    target_22 = bytes.fromhex("9c603865a363a065b70d1912a0859a19a1119c80389c")
    
    # 14字节哈希的目标  
    target_14 = bytes.fromhex("ab7019a76d4d3373b42f3cb5ad3b")
    
    print(f"目标22字节: {target_22.hex()}")
    print(f"目标14字节: {target_14.hex()}")
    
    for password in test_passwords:
        print(f"\n测试密码: {password}")
        
        # 标准哈希
        md5_hash = hashlib.md5(password.encode()).digest()
        sha1_hash = hashlib.sha1(password.encode()).digest()
        sha256_hash = hashlib.sha256(password.encode()).digest()
        
        print(f"  MD5 (16): {md5_hash.hex()}")
        print(f"  SHA1 (20): {sha1_hash.hex()}")
        print(f"  SHA256 (32): {sha256_hash.hex()}")
        
        # 尝试生成22字节哈希
        # 可能是SHA1 + 2字节
        sha1_plus_2 = sha1_hash + b'\x00\x00'
        print(f"  SHA1+00 (22): {sha1_plus_2.hex()}")
        
        sha1_plus_salt = sha1_hash + password[:2].encode()
        print(f"  SHA1+盐 (22): {sha1_plus_salt.hex()}")
        
        # 尝试生成14字节哈希
        # 可能是截断的哈希
        md5_truncated = md5_hash[:14]
        sha1_truncated = sha1_hash[:14]
        
        print(f"  MD5截断 (14): {md5_truncated.hex()}")
        print(f"  SHA1截断 (14): {sha1_truncated.hex()}")
        
        # 检查匹配
        if sha1_plus_2 == target_22:
            print(f"🎯 22字节匹配! SHA1+00: {password}")
        if sha1_plus_salt == target_22:
            print(f"🎯 22字节匹配! SHA1+盐: {password}")
        if md5_truncated == target_14:
            print(f"🎯 14字节匹配! MD5截断: {password}")
        if sha1_truncated == target_14:
            print(f"🎯 14字节匹配! SHA1截断: {password}")

def reverse_engineer_algorithm():
    print(f"\n🔬 逆向工程算法")
    print("="*60)
    
    # 分析所有样本的共同特征
    all_samples = [
        "rT5jnJ1gq2EZLJ1lM2I0pTy1rSZ%3Q",
        "nGA4ZaNjoGW3DRkSoIWaGaERnIA4nD%3Q%3Q",
        "rUAjp21Nq3OZMJ1lM250MTymrTx%3Q",
        "q3AZp21NM3O0Lzy1rTt%3Q",
        "pQWgBKp5GQSgq2qyqTScpauj",
        "qTEcpatjpUqgp3qmGROgpTqlqTAcoKux",
        "nJE4paNjoKq3p0kmoHOapUExnJM4pt%3Q%3Q",
        "nJE4paNjoKq3p0kmoHOapUEjnJ54MN%3Q%3Q",
        "oJE3pxjjoKqap3EmnHO4pN%3Q%3Q"
    ]
    
    decoded_samples = []
    
    for sample in all_samples:
        try:
            decoded = base64.b64decode(sample.replace("%3Q", "="))
            decoded_samples.append(decoded)
            print(f"长度 {len(decoded):2d}: {decoded.hex()}")
        except:
            print(f"解码失败: {sample}")
    
    # 分析长度分布
    lengths = [len(d) for d in decoded_samples]
    length_counts = {}
    for length in lengths:
        length_counts[length] = length_counts.get(length, 0) + 1
    
    print(f"\n长度分布:")
    for length, count in sorted(length_counts.items()):
        print(f"  {length} bytes: {count} 个样本")
    
    # 寻找模式
    print(f"\n模式分析:")
    
    # 检查是否有相同的前缀/后缀
    if len(decoded_samples) > 1:
        # 找最短的样本长度
        min_len = min(len(d) for d in decoded_samples)
        
        # 检查前缀
        for i in range(1, min_len):
            prefixes = set(d[:i].hex() for d in decoded_samples)
            if len(prefixes) == 1:
                print(f"  共同前缀 ({i} bytes): {list(prefixes)[0]}")
        
        # 检查后缀
        for i in range(1, min_len):
            suffixes = set(d[-i:].hex() for d in decoded_samples)
            if len(suffixes) == 1:
                print(f"  共同后缀 ({i} bytes): {list(suffixes)[0]}")

def generate_test_request():
    print(f"\n🚀 生成测试请求")
    print("="*60)
    
    # 基于分析结果，尝试一些可能的密码
    possible_passwords = [
        "admin", "123456", "password", "isdnre", "mnre", "hub",
        "admin123", "test123", "password123"
    ]
    
    for password in possible_passwords:
        print(f"\n测试密码: {password}")
        
        # 生成POST数据
        post_data = urllib.parse.urlencode({
            'inputUrl': '',
            'inputUser': 'isdnre_admin',
            'inputPass': password
        })
        
        print(f"POST数据: {post_data}")

if __name__ == "__main__":
    print("🔍 哈希异常分析 - 寻找真正的加密模式")
    print("="*80)
    
    # 分析异常长度
    analyze_unusual_hash_lengths()
    
    # 测试自定义算法
    test_custom_algorithms()
    
    # 逆向工程
    reverse_engineer_algorithm()
    
    # 生成测试请求
    generate_test_request()
    
    print(f"\n{'='*80}")
    print("🎯 分析结论")
    print(f"{'='*80}")
    print("1. 发现多种异常哈希长度: 14, 18, 22 字节")
    print("2. 可能使用了自定义哈希算法或截断")
    print("3. 22字节可能是SHA1+盐值")
    print("4. 14字节可能是截断的哈希")
    print("5. 需要进一步分析网站源码")
