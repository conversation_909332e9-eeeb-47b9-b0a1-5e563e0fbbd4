import urllib.parse
import base64

original = "nGA4ZaNjoGW3DRkSoIWaGaERnIA4nD%3Q%3Q"
print(f"Original: {original}")

# 方法1: 手动替换可能的编码错误
# %3Q 可能是 %3D (=) 的错误编码
fixed_string = original.replace("%3Q", "%3D")
print(f"Fixed string: {fixed_string}")

url_decoded = urllib.parse.unquote(fixed_string)
print(f"URL decoded: {url_decoded}")

# 尝试Base64解码
try:
    base64_decoded = base64.b64decode(url_decoded)
    print(f"Base64 decoded: {base64_decoded}")
    print(f"As text: {base64_decoded.decode('utf-8', errors='ignore')}")
    # 添加十六进制显示
    hex_result = base64_decoded.hex()
    print(f"Hex: {hex_result}")
    print(f"Length: {len(base64_decoded)} bytes")
except Exception as e:
    print(f"Base64 decoding failed: {e}")

# 方法2: 直接尝试Base64解码原始字符串的前半部分
print("\n--- 直接解码前半部分 ---")
base_part = "nGA4ZaNjoGW3DRkSoIWaGaERnIA4nD"
try:
    # 添加可能缺失的填充
    padded = base_part + "=="
    decoded = base64.b64decode(padded)
    print(f"Direct Base64 decoded: {decoded}")
    print(f"As text: {decoded.decode('utf-8', errors='ignore')}")
except Exception as e:
    print(f"Direct Base64 failed: {e}")

