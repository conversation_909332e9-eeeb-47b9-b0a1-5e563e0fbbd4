import urllib.parse
import base64
import hashlib
import binascii

def analyze_encryption_deeper(encoded_string, field_name):
    print(f"\n{'='*60}")
    print(f"深度分析: {field_name}")
    print(f"{'='*60}")
    print(f"Original: {encoded_string}")

    # 分析字符串特征
    print(f"\n📊 字符串特征分析:")
    print(f"- 长度: {len(encoded_string)}")
    print(f"- 包含%3Q: {'%3Q' in encoded_string}")
    print(f"- 字符集: {set(encoded_string)}")

    # 尝试多种解码方法
    print(f"\n🔍 尝试多种解码方法:")

    # 方法1: 直接Base64 (去掉%3Q)
    try:
        clean_string = encoded_string.replace("%3Q", "=").replace("%3D", "=")
        decoded1 = base64.b64decode(clean_string)
        print(f"1. 直接Base64解码: {decoded1.hex()}")
        print(f"   长度: {len(decoded1)} bytes")
        print(f"   可能的文本: {decoded1.decode('utf-8', errors='ignore')}")
    except Exception as e:
        print(f"1. 直接Base64解码失败: {e}")

    # 方法2: URL解码后Base64
    try:
        url_decoded = urllib.parse.unquote(encoded_string.replace("%3Q", "%3D"))
        decoded2 = base64.b64decode(url_decoded)
        print(f"2. URL+Base64解码: {decoded2.hex()}")
        print(f"   长度: {len(decoded2)} bytes")
    except Exception as e:
        print(f"2. URL+Base64解码失败: {e}")

    # 方法3: 自定义字符替换
    try:
        # 尝试不同的字符映射
        custom_decode = encoded_string
        for old, new in [("%3Q", "="), ("3Q", "=="), ("Q", "=")]:
            try:
                test_string = encoded_string.replace(old, new)
                decoded3 = base64.b64decode(test_string)
                print(f"3. 替换'{old}'->''{new}': {decoded3.hex()}")
                break
            except:
                continue
    except Exception as e:
        print(f"3. 自定义替换失败: {e}")

    # 方法4: 分析是否为哈希值
    print(f"\n🔐 哈希值分析:")
    if len(encoded_string.replace("%3Q", "")) % 4 == 0:
        try:
            clean_for_hash = encoded_string.replace("%3Q", "=")
            hash_candidate = base64.b64decode(clean_for_hash)
            print(f"- 可能的哈希长度: {len(hash_candidate)} bytes")

            if len(hash_candidate) == 16:
                print("- 可能是MD5哈希 (16字节)")
            elif len(hash_candidate) == 20:
                print("- 可能是SHA1哈希 (20字节)")
            elif len(hash_candidate) == 32:
                print("- 可能是SHA256哈希 (32字节)")
            else:
                print(f"- 未知哈希类型 ({len(hash_candidate)}字节)")

        except Exception as e:
            print(f"- 哈希分析失败: {e}")

def test_password_hashing():
    print(f"\n{'='*60}")
    print("密码哈希测试")
    print(f"{'='*60}")

    # 测试常见密码
    test_passwords = ["admin", "123456", "password", "test", "admin123"]

    for password in test_passwords:
        print(f"\n测试密码: '{password}'")

        # MD5
        md5_hash = hashlib.md5(password.encode()).digest()
        md5_b64 = base64.b64encode(md5_hash).decode()
        print(f"MD5+Base64: {md5_b64}")

        # SHA1
        sha1_hash = hashlib.sha1(password.encode()).digest()
        sha1_b64 = base64.b64encode(sha1_hash).decode()
        print(f"SHA1+Base64: {sha1_b64}")

        # 自定义编码 (替换=为%3Q)
        md5_custom = md5_b64.replace("=", "%3Q")
        sha1_custom = sha1_b64.replace("=", "%3Q")
        print(f"MD5+自定义: {md5_custom}")
        print(f"SHA1+自定义: {sha1_custom}")

def reverse_engineer_samples():
    print(f"\n{'='*60}")
    print("样本逆向工程")
    print(f"{'='*60}")

    # 已知的样本
    samples = [
        "rT5jnJ1gq2EZLJ1lM2I0pTy1rSZ%3Q",
        "nGA4ZaNjoGW3DRkSoIWaGaERnIA4nD%3Q%3Q",
        "rUAjp21Nq3OZMJ1lM250MTymrTx%3Q",
        "q3AZp21NM3O0Lzy1rTt%3Q"
    ]

    for sample in samples:
        analyze_encryption_deeper(sample, f"Sample")

        # 尝试匹配常见密码
        try:
            decoded_bytes = base64.b64decode(sample.replace("%3Q", "="))
            print(f"尝试匹配常见密码哈希...")

            # 测试常见密码
            common_passwords = ["admin", "123456", "password", "test", "admin123", "12345", "qwerty"]
            for pwd in common_passwords:
                md5_hash = hashlib.md5(pwd.encode()).digest()
                sha1_hash = hashlib.sha1(pwd.encode()).digest()

                if md5_hash == decoded_bytes:
                    print(f"🎯 匹配! 密码可能是: '{pwd}' (MD5)")
                if sha1_hash == decoded_bytes:
                    print(f"🎯 匹配! 密码可能是: '{pwd}' (SHA1)")

        except Exception as e:
            print(f"匹配测试失败: {e}")

        print("-" * 40)

if __name__ == "__main__":
    print("🔍 深度加密分析 - 寻找真正的加密方法")
    print("="*80)

    # 执行深度分析
    test_password_hashing()
    reverse_engineer_samples()

    print(f"\n{'='*80}")
    print("🎯 寻找真正的加密算法")
    print(f"{'='*80}")

    # 重点分析一个样本
    sample = "rT5jnJ1gq2EZLJ1lM2I0pTy1rSZ%3Q"
    print(f"重点分析样本: {sample}")

    # 解码并分析
    try:
        decoded = base64.b64decode(sample.replace("%3Q", "="))
        print(f"解码结果: {decoded.hex()}")
        print(f"长度: {len(decoded)} bytes")

        # 尝试各种常见密码的哈希
        print(f"\n🔍 暴力匹配常见密码:")
        common_passwords = [
            "admin", "123456", "password", "test", "admin123",
            "12345", "qwerty", "abc123", "password123", "admin",
            "root", "toor", "pass", "123", "1234", "12345678",
            "welcome", "login", "guest", "user", "demo"
        ]

        found = False
        for pwd in common_passwords:
            # MD5
            md5_hash = hashlib.md5(pwd.encode()).digest()
            if md5_hash == decoded:
                print(f"🎯 找到匹配! 密码: '{pwd}' (MD5)")
                found = True

            # SHA1
            sha1_hash = hashlib.sha1(pwd.encode()).digest()
            if sha1_hash == decoded:
                print(f"🎯 找到匹配! 密码: '{pwd}' (SHA1)")
                found = True

            # 尝试加盐
            for salt in ["", "salt", "admin", "mnre", "hub"]:
                salted_pwd = pwd + salt
                md5_salted = hashlib.md5(salted_pwd.encode()).digest()
                sha1_salted = hashlib.sha1(salted_pwd.encode()).digest()

                if md5_salted == decoded:
                    print(f"🎯 找到匹配! 密码: '{pwd}' + 盐: '{salt}' (MD5)")
                    found = True
                if sha1_salted == decoded:
                    print(f"🎯 找到匹配! 密码: '{pwd}' + 盐: '{salt}' (SHA1)")
                    found = True

        if not found:
            print("❌ 未找到匹配的常见密码")
            print("💡 可能使用了:")
            print("   - 复杂密码")
            print("   - 自定义哈希算法")
            print("   - 多重哈希")
            print("   - 特殊盐值")

    except Exception as e:
        print(f"分析失败: {e}")

    print(f"\n{'='*80}")
    print("🔧 下一步建议")
    print(f"{'='*80}")
    print("1. 尝试更多密码组合")
    print("2. 分析网站源码中的哈希算法")
    print("3. 检查是否有自定义加密函数")
    print("4. 尝试字典攻击")
    print("5. 分析数据库中的其他字段模式")


