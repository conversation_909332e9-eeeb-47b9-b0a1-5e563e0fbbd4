import urllib.parse
import base64

def decode_field(encoded_string, field_name):
    print(f"\n--- 解码 {field_name} ---")
    print(f"Original: {encoded_string}")
    
    # 修复 %3Q 为 %3D
    fixed_string = encoded_string.replace("%3Q", "%3D")
    print(f"Fixed: {fixed_string}")
    
    # URL解码
    url_decoded = urllib.parse.unquote(fixed_string)
    print(f"URL decoded: {url_decoded}")
    
    # Base64解码
    try:
        base64_decoded = base64.b64decode(url_decoded)
        print(f"Base64 decoded: {base64_decoded}")
        print(f"As text: {base64_decoded.decode('utf-8', errors='ignore')}")
        print(f"Hex: {base64_decoded.hex()}")
        print(f"Length: {len(base64_decoded)} bytes")
    except Exception as e:
        print(f"Base64 decoding failed: {e}")

# 解码密码字段
decode_field("rT5jnJ1gq2EZLJ1lM2I0pTy1rSZ%3Q", "sy_stf_password")

# 解码用户名字段  
decode_field("rT5jnJ1gq2EZDJ1vM2I0q2yyrSp%3Q", "sy_stf_username")


